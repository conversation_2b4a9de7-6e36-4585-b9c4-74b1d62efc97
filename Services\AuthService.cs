using stage.DTOs.Auth;
using stage.DTOs.User; // Pour UtilisateurDto
using stage.Models;
using stage.Repositories;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity; // Pour UserManager, SignInManager, RoleManager

namespace stage.Services
{
    public class AuthService : IAuthService
    {
        private readonly UserManager<Applicationuser> _userManager;
        private readonly SignInManager<Applicationuser> _signInManager;
        private readonly RoleManager<IdentityRole<Guid>> _roleManager; // Injecté pour gérer les rôles
        private readonly ISocieteRepository _societeRepository;
        private readonly IParametrageRepository _parametrageRepository;
        private readonly IJwtService _jwtService;

        public AuthService(UserManager<Applicationuser> userManager,
                           SignInManager<Applicationuser> signInManager,
                           RoleManager<IdentityRole<Guid>> roleManager, // Injection
                           ISocieteRepository societeRepository,
                           IParametrageRepository parametrageRepository,
                           IJwtService jwtService)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager; // Assignation
            _societeRepository = societeRepository;
            _parametrageRepository = parametrageRepository;
            _jwtService = jwtService;
        }

        public async Task<AuthResponseDto?> RegisterAdminAndSociete(UserRegisterDto registerDto)
        {
            Console.WriteLine($"Starting registration for email: {registerDto.Email}");

            // 1. Vérifier si l'email existe déjà
            var existingUser = await _userManager.FindByEmailAsync(registerDto.Email);
            if (existingUser != null)
            {
                Console.WriteLine($"Email {registerDto.Email} already exists");
                return null; // Email déjà utilisé
            }

            Console.WriteLine("Email is available, proceeding with registration");

            // 2. Créer la nouvelle société
            var nouvelleSociete = new Societe
            {
                Id = Guid.NewGuid(),
                Nom = registerDto.SocieteNom,
                Adresse = registerDto.SocieteAdresse,
                Logo = null
            };
            await _societeRepository.AddAsync(nouvelleSociete);

            // 3. Créer le paramétrage pour cette société
            var nouveauParametrage = new Parametrage
            {
                Id = Guid.NewGuid(),
                Signature = registerDto.ParametrageSignature,
                Cachet = registerDto.ParametrageCachet,
                Adresse = registerDto.ParametrageAdresse,
                SocieteId = nouvelleSociete.Id
            };
            await _parametrageRepository.AddAsync(nouveauParametrage);

            // 4. Créer l'utilisateur admin
            var nouvelAdmin = new Applicationuser
            {
                Id = Guid.NewGuid(), // Générer l'ID ici
                Nom = registerDto.Nom,
                Email = registerDto.Email,
                UserName = registerDto.Email, // Utiliser l'email comme nom d'utilisateur pour Identity
                SocieteId = nouvelleSociete.Id
                // La propriété de navigation Societe sera chargée plus tard si nécessaire, ou passée si déjà chargée
            };

            var createResult = await _userManager.CreateAsync(nouvelAdmin, registerDto.MotDePasse);
            if (!createResult.Succeeded)
            {
                // En cas d'échec de création d'utilisateur, annuler la création de la société et du paramétrage
                _parametrageRepository.Remove(nouveauParametrage);
                _societeRepository.Remove(nouvelleSociete);
                await _societeRepository.SaveChangesAsync(); // Sauvegarder l'annulation
                var errors = string.Join(", ", createResult.Errors.Select(e => e.Description));
                Console.WriteLine($"Error creating user: {errors}");
                // Log the specific errors for debugging
                foreach (var error in createResult.Errors)
                {
                    Console.WriteLine($"Identity Error - Code: {error.Code}, Description: {error.Description}");
                }
                return null;
            }

            // Assigner le rôle "Admin"
            // Vérifier si le rôle existe (important si non seedé)
            if (!await _roleManager.RoleExistsAsync("Admin"))
            {
                await _roleManager.CreateAsync(new IdentityRole<Guid>("Admin"));
            }
            // Assigner le rôle à l'utilisateur
            await _userManager.AddToRoleAsync(nouvelAdmin, "Admin");

            // Sauvegarder les changements pour la société et le paramétrage
            await _societeRepository.SaveChangesAsync();

            // Récupérer les rôles de l'utilisateur (sera juste "Admin" ici)
            var roles = await _userManager.GetRolesAsync(nouvelAdmin);
            var token = _jwtService.GenerateToken(nouvelAdmin, roles, nouvelAdmin.SocieteId);

            // Mappage manuel vers UtilisateurDto pour la réponse
            var userDto = new UtilisateurDto
            {
                Id = nouvelAdmin.Id,
                Nom = nouvelAdmin.Nom,
                Email = nouvelAdmin.Email,
                Role = roles.FirstOrDefault() ?? "N/A", // Puisqu'un seul rôle, prenez le premier
                SocieteId = nouvelAdmin.SocieteId,
                SocieteNom = nouvelleSociete.Nom // Nom de la société créée
            };

            return new AuthResponseDto
            {
                Token = token,
                Message = "Inscription et société créées avec succès.",
                User = userDto
            };
        }

        public async Task<AuthResponseDto?> Login(UserLoginDto loginDto)
        {
            var user = await _userManager.FindByEmailAsync(loginDto.Email);

            if (user == null)
            {
                return null; // Utilisateur non trouvé
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, loginDto.MotDePasse, lockoutOnFailure: false);

            if (!result.Succeeded)
            {
                return null; // Mot de passe invalide
            }

            // Charger les rôles et le nom de la société pour le JWT et le DTO
            var roles = await _userManager.GetRolesAsync(user);
            var token = _jwtService.GenerateToken(user, roles, user.SocieteId);

            var societe = await _societeRepository.GetByIdAsync(user.SocieteId); // Charger la société pour le nom

            // Mappage manuel vers UtilisateurDto pour la réponse
            var userDto = new UtilisateurDto
            {
                Id = user.Id,
                Nom = user.Nom,
                Email = user.Email,
                Role = roles.FirstOrDefault() ?? "N/A", // Puisqu'un seul rôle attendu
                SocieteId = user.SocieteId,
                SocieteNom = societe?.Nom
            };

            return new AuthResponseDto
            {
                Token = token,
                Message = "Connexion réussie.",
                User = userDto
            };
        }

        public async Task<object> LogoutAsync()
        {
            // Pour une API JWT stateless, le logout côté serveur est principalement informatif
            // Le client doit supprimer le token de son stockage local

            // Optionnel : Log de l'événement de déconnexion
            Console.WriteLine($"User logged out at {DateTime.UtcNow}");

            // Dans une implémentation plus avancée, vous pourriez :
            // 1. Ajouter le token à une blacklist
            // 2. Réduire la durée de vie du token
            // 3. Invalider les refresh tokens si vous en utilisez

            return new
            {
                Success = true,
                Message = "Déconnexion réussie. Veuillez supprimer le token de votre stockage local.",
                Timestamp = DateTime.UtcNow
            };
        }
    }
}