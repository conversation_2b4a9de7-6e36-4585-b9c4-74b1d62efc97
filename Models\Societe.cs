﻿using System.Text.RegularExpressions;

namespace stage.Models
{
    public class Societe
    {
        public Guid Id { get; set; }
        public string Nom { get; set; }
        public string Adresse { get; set; }
        public string? Logo { get; set; }
        public Parametrage Parametrage { get; set; }
        public Guid ParametrageId { get; set; } 
        public ICollection<Applicationuser> Utilisateurs { get; set; }
        public ICollection<Client> Clients { get; set; }
        public ICollection<Fournisseur> Fournisseurs { get; set; }
        public ICollection<Facture> Factures { get; set; }
        public ICollection<Document> Documents { get; set; }

    }
}