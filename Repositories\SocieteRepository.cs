using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class SocieteRepository : BaseRepository<Societe>, ISocieteRepository
    {
        public SocieteRepository(StageContext context) : base(context)
        {
        }

        public async Task<Societe?> GetSocieteWithParametrageAsync(Guid societeId)
        {
            return await _dbSet
                .Include(s => s.Parametrage)
                .Include(s => s.Utilisateurs)
                .FirstOrDefaultAsync(s => s.Id == societeId);
        }

        public async Task<Societe?> GetSocieteByNomAsync(string nom)
        {
            return await _dbSet
                .Include(s => s.Parametrage)
                .FirstOrDefaultAsync(s => s.Nom == nom);
        }

        public override async Task<Societe?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(s => s.Parametrage)
                .Include(s => s.Utilisateurs)
                .Include(s => s.Clients)
                .Include(s => s.Fournisseurs)
                .FirstOrDefaultAsync(s => s.Id == id);
        }
    }
}
