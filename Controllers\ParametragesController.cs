﻿using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")] 
    public class ParametragesController : ControllerBase
    {
        private readonly IParametrageRepository _parametrageRepository;
        private readonly ISocieteRepository _societeRepository; 

        public ParametragesController(IParametrageRepository parametrageRepository, ISocieteRepository societeRepository)
        {
            _parametrageRepository = parametrageRepository;
            _societeRepository = societeRepository;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid.");
            }
            return societeId;
        }

        [HttpGet]
        public async Task<ActionResult<Parametrage>> GetParametrage()
        {
            var societeId = GetUserSocieteId();
            var parametrage = await _parametrageRepository.FindAsync(p => p.SocieteId == societeId);

            var firstParametrage = parametrage.FirstOrDefault();

            if (firstParametrage == null)
            {
                return NotFound("Aucun paramétrage trouvé pour cette société.");
            }

            return Ok(firstParametrage);
        }
        [HttpPut]
        public async Task<IActionResult> PutParametrage([FromBody] Parametrage parametrage)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();

            if (parametrage.SocieteId != societeId)
            {
                return BadRequest("Le paramétrage fourni ne correspond pas à votre société.");
            }

            var existingParametrage = await _parametrageRepository.GetByIdAsync(parametrage.Id);
            if (existingParametrage == null || existingParametrage.SocieteId != societeId)
            {
                return NotFound("Paramétrage non trouvé ou non associé à votre société.");
            }

            existingParametrage.Signature = parametrage.Signature;
            existingParametrage.Cachet = parametrage.Cachet;
            existingParametrage.Adresse = parametrage.Adresse;

            _parametrageRepository.Update(existingParametrage);

            try
            {
                await _parametrageRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await ParametrageExists(parametrage.Id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        private async Task<bool> ParametrageExists(Guid id, Guid societeId)
        {
            return (await _parametrageRepository.FindAsync(p => p.Id == id && p.SocieteId == societeId)).Any();
        }
    }
}