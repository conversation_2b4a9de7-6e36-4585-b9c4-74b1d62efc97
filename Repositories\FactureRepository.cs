using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class FactureRepository : BaseRepository<Facture>, IFactureRepository
    {
        public FactureRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Facture>> GetFacturesBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Client)
                .Include(f => f.Fournis<PERSON>ur)
                .Include(f => f.Societe)
                .Where(f => f.SocieteId == societeId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Facture>> GetFacturesByTypeAsync(string type, Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Client)
                .Include(f => f.Fournisseur)
                .Include(f => f.Societe)
                .Where(f => f.Type == type && f.SocieteId == societeId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Facture>> GetFacturesByClientIdAsync(Guid clientId, Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Client)
                .Include(f => f.Fournisseur)
                .Include(f => f.Societe)
                .Where(f => f.ClientId == clientId && f.SocieteId == societeId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Facture>> GetFacturesByFournisseurIdAsync(Guid fournisseurId, Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Client)
                .Include(f => f.Fournisseur)
                .Include(f => f.Societe)
                .Where(f => f.FournisseurId == fournisseurId && f.SocieteId == societeId)
                .ToListAsync();
        }

        public override async Task<Facture?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(f => f.Client)
                .Include(f => f.Fournisseur)
                .Include(f => f.Societe)
                .FirstOrDefaultAsync(f => f.Id == id);
        }
    }
}
