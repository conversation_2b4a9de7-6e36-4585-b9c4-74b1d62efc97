using System.Threading.Tasks;

namespace stage.Services
{
    public interface IRoleService
    {
        /// <summary>
        /// Initialise les rôles par défaut du système (Admin et User)
        /// </summary>
        Task InitializeDefaultRolesAsync();

        /// <summary>
        /// Vérifie si un rôle existe
        /// </summary>
        /// <param name="roleName">Nom du rôle</param>
        /// <returns>True si le rôle existe, False sinon</returns>
        Task<bool> RoleExistsAsync(string roleName);

        /// <summary>
        /// Obtient la liste des rôles disponibles
        /// </summary>
        /// <returns>Liste des noms de rôles</returns>
        Task<List<string>> GetAvailableRolesAsync();
    }
}
