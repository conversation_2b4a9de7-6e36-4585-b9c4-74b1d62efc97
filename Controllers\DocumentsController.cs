﻿using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore; // Pour .Include() et .ToListAsync()

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize] // N'importe quel utilisateur authentifié
    public class DocumentsController : ControllerBase
    {
        private readonly IDocumentRepository _documentRepository;
        private readonly ITypeDocumentRepository _typeDocumentRepository;
        // La Societe est déjà accessible via le claim SocieteId,
        // et le chargement via Include() est suffisant pour l'afficher si nécessaire.

        public DocumentsController(IDocumentRepository documentRepository,
                                   ITypeDocumentRepository typeDocumentRepository)
        {
            _documentRepository = documentRepository;
            _typeDocumentRepository = typeDocumentRepository;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                // Cela ne devrait pas arriver avec [Authorize] si le token est bien formé
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT. User not properly authenticated.");
            }
            return societeId;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Document>>> GetDocuments()
        {
            var societeId = GetUserSocieteId();
            // Inclure TypeDocument et Societe pour avoir les objets complets si nécessaire
            var documents = await _documentRepository.GetQueryable()
                                                     .Where(d => d.SocieteId == societeId)
                                                     .Include(d => d.TypeDocument)
                                                     .Include(d => d.Societe) // Inclure Societe si vous voulez ses détails dans la réponse
                                                     .ToListAsync();
            return Ok(documents);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Document>> GetDocument(Guid id)
        {
            var societeId = GetUserSocieteId();
            // Inclure TypeDocument et Societe
            var document = await _documentRepository.GetQueryable()
                                                     .Include(d => d.TypeDocument)
                                                     .Include(d => d.Societe) // Inclure Societe si vous voulez ses détails dans la réponse
                                                     .FirstOrDefaultAsync(d => d.Id == id);

            if (document == null || document.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(document);
        }

        [HttpPost]
        public async Task<ActionResult<Document>> PostDocument([FromBody] Document document)
        {
            // Valider le modèle avant de traiter
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();

            // Vérifier que le TypeDocumentId est valide et appartient à la même société
            // Note: document.TypeDocumentId est de type Guid, et non nullable ici
            var typeDoc = await _typeDocumentRepository.GetByIdAsync(document.TypeDocumentId);
            if (typeDoc == null || typeDoc.SocieteId != societeId)
            {
                return BadRequest("Le type de document spécifié n'existe pas ou n'appartient pas à votre société.");
            }

            // Assigner les propriétés gérées par le serveur
            document.Id = Guid.NewGuid();
            document.SocieteId = societeId; // Assigne l'ID de la société de l'utilisateur (issu du token)
            document.DateCreation = DateTime.UtcNow; // Assigner la date de création

            // S'assurer que les objets de navigation sont null pour éviter les problèmes de suivi d'EF Core
            // Ils seront rechargés par EF Core si nécessaire après la sauvegarde, ou si vous faites un .Include()
            document.TypeDocument = null;
            document.Societe = null;

            await _documentRepository.AddAsync(document);
            await _documentRepository.SaveChangesAsync();

            // Recharger le document avec ses relations pour la réponse si nécessaire
            var createdDocument = await _documentRepository.GetQueryable()
                                                            .Include(d => d.TypeDocument)
                                                            .Include(d => d.Societe)
                                                            .FirstOrDefaultAsync(d => d.Id == document.Id);

            return CreatedAtAction(nameof(GetDocument), new { id = createdDocument.Id }, createdDocument);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutDocument(Guid id, [FromBody] Document document)
        {
            // S'assurer que l'ID dans l'URL correspond à l'ID dans le corps de la requête
            if (id != document.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingDocument = await _documentRepository.GetByIdAsync(id);

            // Vérifier si le document existe et appartient à la société de l'utilisateur
            if (existingDocument == null || existingDocument.SocieteId != societeId)
            {
                return NotFound();
            }

            // Vérifier que le TypeDocumentId est valide et appartient à la même société
            var typeDoc = await _typeDocumentRepository.GetByIdAsync(document.TypeDocumentId);
            if (typeDoc == null || typeDoc.SocieteId != societeId)
            {
                return BadRequest("Le type de document spécifié n'existe pas ou n'appartient pas à votre société.");
            }

            // Mettre à jour les propriétés modifiables du document existant
            existingDocument.Titre = document.Titre;
            existingDocument.Contenu = document.Contenu;
            existingDocument.TypeDocumentId = document.TypeDocumentId;
            // DateCreation et SocieteId ne doivent normalement pas être modifiés par l'utilisateur
            // existingDocument.DateCreation = document.DateCreation; // Généralement non modifiable en PUT

            // Le TypeDocument et Societe existants ne doivent pas être définis directement sur existingDocument
            // si vous n'avez pas rechargé l'entité entière avec .Include().
            // Seules les clés étrangères (TypeDocumentId, SocieteId) sont nécessaires.
            // Si vous voulez que la propriété de navigation soit à jour pour les opérations ultérieures sans recharge,
            // vous pouvez l'attacher, mais ce n'est pas strictement nécessaire pour la sauvegarde.
            // existingDocument.TypeDocument = typeDoc; // Optionnel

            _documentRepository.Update(existingDocument);

            try
            {
                await _documentRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await DocumentExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDocument(Guid id)
        {
            var societeId = GetUserSocieteId();
            var document = await _documentRepository.GetByIdAsync(id);

            if (document == null || document.SocieteId != societeId)
            {
                return NotFound();
            }

            _documentRepository.Remove(document);
            await _documentRepository.SaveChangesAsync();

            return NoContent();
        }

        private async Task<bool> DocumentExists(Guid id, Guid societeId)
        {
            // Vérifie si un document avec cet ID existe et appartient à la société donnée
            return (await _documentRepository.FindAsync(d => d.Id == id && d.SocieteId == societeId)).Any();
        }
    }
}