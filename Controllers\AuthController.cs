using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using stage.DTOs.Auth;
using stage.Services;
using System.Threading.Tasks;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        /// <summary>
        /// Enregistre un nouvel administrateur et crée une société associée.
        /// C'est le point d'entrée pour le premier utilisateur/société.
        /// </summary>
        /// <param name="registerDto">Les informations d'inscription de l'utilisateur et de la société.</param>
        /// <returns>Un token JWT et les informations de l'utilisateur en cas de succès.</returns>
        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] UserRegisterDto registerDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var response = await _authService.RegisterAdminAndSociete(registerDto);

            if (response == null)
            {
                // Message plus générique pour éviter d'indiquer si l'email existe déjà vs autre erreur
                return Conflict("Échec de l'inscription. L'adresse email est peut-être déjà utilisée ou une autre erreur est survenue.");
            }

            return Ok(response);
        }

        /// <summary>
        /// Authentifie un utilisateur et retourne un token JWT.
        /// </summary>
        /// <param name="loginDto">Les informations de connexion de l'utilisateur.</param>
        /// <returns>Un token JWT et les informations de l'utilisateur en cas de succès.</returns>
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] UserLoginDto loginDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var response = await _authService.Login(loginDto);

            if (response == null)
            {
                return Unauthorized("Email ou mot de passe invalide.");
            }

            return Ok(response);
        }

        /// <summary>
        /// Déconnecte l'utilisateur en invalidant son token JWT.
        /// </summary>
        /// <returns>Message de confirmation de déconnexion.</returns>
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            var response = await _authService.LogoutAsync();
            return Ok(response);
        }
    }
}