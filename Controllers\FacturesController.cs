﻿using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize] 
    public class FacturesController : ControllerBase
    {
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository; 

        public FacturesController(IFactureRepository factureRepository, IClientRepository clientRepository)
        {
            _factureRepository = factureRepository;
            _clientRepository = clientRepository;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Facture>>> GetFactures()
        {
            var societeId = GetUserSocieteId();
            var factures = await _factureRepository.GetQueryable()
                                                    .Where(f => f.SocieteId == societeId)
                                                    .Include(f => f.Client)
                                                    .ToListAsync();
            return Ok(factures);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Facture>> GetFacture(Guid id)
        {
            var societeId = GetUserSocieteId();
            var facture = await _factureRepository.GetQueryable()
                                                    .Include(f => f.Client)
                                                    .FirstOrDefaultAsync(f => f.Id == id);

            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(facture);
        }

        [HttpPost]
        public async Task<ActionResult<Facture>> PostFacture([FromBody] Facture facture)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();

            // Vérifier que le client de la facture appartient à la même société
            if (facture.ClientId.HasValue)
            {
                var client = await _clientRepository.GetByIdAsync(facture.ClientId.Value);
                if (client == null || client.SocieteId != societeId)
                {
                    return BadRequest("Le client spécifié n'existe pas ou n'appartient pas à votre société.");
                }
            }

            facture.Id = Guid.NewGuid();
            facture.SocieteId = societeId; // Assigne l'ID de la société de l'utilisateur
            facture.Date = DateTime.UtcNow; // Assigner la date de création

            await _factureRepository.AddAsync(facture);
            await _factureRepository.SaveChangesAsync();

            // Recharger la facture avec les entités liées pour la réponse si nécessaire
            var createdFacture = await _factureRepository.GetQueryable()
                                                        .Include(f => f.Client)
                                                        .FirstOrDefaultAsync(f => f.Id == facture.Id);

            return CreatedAtAction(nameof(GetFacture), new { id = createdFacture.Id }, createdFacture);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutFacture(Guid id, [FromBody] Facture facture)
        {
            if (id != facture.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingFacture = await _factureRepository.GetByIdAsync(id);

            if (existingFacture == null || existingFacture.SocieteId != societeId)
            {
                return NotFound();
            }

            // Vérifier que le client mis à jour appartient à la même société
            if (facture.ClientId.HasValue)
            {
                var client = await _clientRepository.GetByIdAsync(facture.ClientId.Value);
                if (client == null || client.SocieteId != societeId)
                {
                    return BadRequest("Le client spécifié n'existe pas ou n'appartient pas à votre société.");
                }
            }

            existingFacture.Date = facture.Date;
            existingFacture.Montant = facture.Montant;
            _factureRepository.Update(existingFacture);

            try
            {
                await _factureRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await FactureExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFacture(Guid id)
        {
            var societeId = GetUserSocieteId();
            var facture = await _factureRepository.GetByIdAsync(id);

            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            _factureRepository.Remove(facture);
            await _factureRepository.SaveChangesAsync();

            return NoContent();
        }

        private async Task<bool> FactureExists(Guid id, Guid societeId)
        {
            return (await _factureRepository.FindAsync(f => f.Id == id && f.SocieteId == societeId)).Any();
        }
    }
}