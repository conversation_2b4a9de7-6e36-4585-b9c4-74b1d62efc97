using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace stage.Services
{
    public class RoleService : IRoleService
    {
        private readonly RoleManager<IdentityRole<Guid>> _roleManager;

        // Constantes pour les rôles du système
        public const string ADMIN_ROLE = "Admin";
        public const string USER_ROLE = "User";

        public RoleService(RoleManager<IdentityRole<Guid>> roleManager)
        {
            _roleManager = roleManager;
        }

        /// <summary>
        /// Initialise les rôles par défaut du système (Admin et User)
        /// </summary>
        public async Task InitializeDefaultRolesAsync()
        {
            // Créer le rôle Admin s'il n'existe pas
            if (!await _roleManager.RoleExistsAsync(ADMIN_ROLE))
            {
                await _roleManager.CreateAsync(new IdentityRole<Guid>(ADMIN_ROLE));
            }

            // Créer le rôle User s'il n'existe pas
            if (!await _roleManager.RoleExistsAsync(USER_ROLE))
            {
                await _roleManager.CreateAsync(new IdentityRole<Guid>(USER_ROLE));
            }
        }

        /// <summary>
        /// Vérifie si un rôle existe
        /// </summary>
        /// <param name="roleName">Nom du rôle</param>
        /// <returns>True si le rôle existe, False sinon</returns>
        public async Task<bool> RoleExistsAsync(string roleName)
        {
            return await _roleManager.RoleExistsAsync(roleName);
        }

        /// <summary>
        /// Obtient la liste des rôles disponibles
        /// </summary>
        /// <returns>Liste des noms de rôles</returns>
        public async Task<List<string>> GetAvailableRolesAsync()
        {
            var roles = await _roleManager.Roles.Select(r => r.Name).ToListAsync();
            return roles ?? new List<string>();
        }
    }
}
