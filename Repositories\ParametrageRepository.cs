using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class ParametrageRepository : BaseRepository<Parametrage>, IParametrageRepository
    {
        public ParametrageRepository(StageContext context) : base(context)
        {
        }

        public async Task<Parametrage?> GetParametrageBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(p => p.Societe)
                .FirstOrDefaultAsync(p => p.SocieteId == societeId);
        }

        public override async Task<Parametrage?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(p => p.Societe)
                .FirstOrDefaultAsync(p => p.Id == id);
        }
    }
}
