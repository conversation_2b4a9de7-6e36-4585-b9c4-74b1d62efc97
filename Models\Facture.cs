﻿namespace stage.Models
{
    public class Facture
    {
        public Guid Id { get; set; }
        public string Numero { get; set; }
        public string Type { get; set; } // "Achat" ou "Vente"
        public decimal Montant { get; set; }
        public DateTime Date { get; set; }
        public Guid? FournisseurId { get; set; }
        public Fournisseur? Fournisseur { get; set; }

        public Guid? ClientId { get; set; }
        public Client? Client { get; set; }

        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
    }
}