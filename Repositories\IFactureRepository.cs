using stage.Models;

namespace stage.Repositories
{
    public interface IFactureRepository : IBaseRepository<Facture>
    {
        Task<IEnumerable<Facture>> GetFacturesBySocieteIdAsync(Guid societeId);
        Task<IEnumerable<Facture>> GetFacturesByTypeAsync(string type, Guid societeId);
        Task<IEnumerable<Facture>> GetFacturesByClientIdAsync(Guid clientId, Guid societeId);
        Task<IEnumerable<Facture>> GetFacturesByFournisseurIdAsync(Guid fournisseurId, Guid societeId);
    }
}
